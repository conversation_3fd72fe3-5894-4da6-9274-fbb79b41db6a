package http

import (
	"fmt"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/domain"
)

// PresensiHandler struct
type PresensiHandler struct {
	PresensiUseCase domain.PresensiUseCase
}

// NewPresensiHandler func
// @Summary Initialize presensi routes
// @Description Set up presensi related endpoints
func NewPresensiHandler(app *fiber.App, uc domain.PresensiUseCase) {
	handler := &PresensiHandler{PresensiUseCase: uc}
	v1 := app.Group("/v1")
	v1.Get("/presensi", handler.Fetch)
}

// Fetch get all presensi
// @Summary Get all attendance records
// @Description Retrieve all employee attendance records including late status, overtime, and break times
// @Tags presensi
// @Accept json
// @Produce json
// @Success 200 {array} domain.Presensi "List of attendance records"
// @Failure 500 {object} error "Internal server error"
// @Router /v1/presensi [get]
func (p *PresensiHandler) Fetch(c *fiber.Ctx) error {
	presensi, err := p.PresensiUseCase.Fetch()
	if err != nil {
		fmt.Println("error: ", err)
		return nil
	}
	return c.JSON(presensi)
}

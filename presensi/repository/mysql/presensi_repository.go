package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLPresensiRepository struct {
	mysql.Repository
}

// NewMySQLPresensiRepository func
func NewMySQLPresensiRepository(conn *sql.DB) domain.PresensiRepository {
	return &mySQLPresensiRepository{mysql.Repository{Conn: conn}}
}

func (m *mySQLPresensiRepository) Fetch() ([]domain.Presensi, error) {
	results, err := m.QueryArrayOld("select * from hrm_trans_import_presensi")
	if err != nil {
		log.Printf("getting schedule error: %v\n", err)
		return nil, err
	}

	log.Printf("total data: %d", len(results))

	resultJSON, err := json.<PERSON>(results)
	if err != nil {
		log.Printf("marshalling error: %v\n", err)
	}

	var result []domain.Presensi
	err = json.Unmarshal(resultJSON, &result)
	if err != nil {
		log.Printf("unmarshalling error: %v\n", err)
	}
	return result, nil
}

func (m *mySQLPresensiRepository) ValidateEmployeeNIKs(niks []string) ([]domain.EmployeeNIK, error) {
	if len(niks) == 0 {
		return []domain.EmployeeNIK{}, nil
	}

	// Create placeholders for IN clause
	placeholders := make([]string, len(niks))
	args := make([]any, len(niks))
	for i, nik := range niks {
		placeholders[i] = "?"
		args[i] = nik
	}

	query := fmt.Sprintf("SELECT employee_fkid, nik FROM hrm_employee WHERE nik IN (%s)", strings.Join(placeholders, ","))

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.Printf("validating employee NIKs error: %v\n", err)
		return nil, err
	}

	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.Printf("marshalling employee NIKs error: %v\n", err)
		return nil, err
	}

	var employeeNIKs []domain.EmployeeNIK
	err = json.Unmarshal(resultJSON, &employeeNIKs)
	if err != nil {
		log.Printf("unmarshalling employee NIKs error: %v\n", err)
		return nil, err
	}

	return employeeNIKs, nil
}

func (m *mySQLPresensiRepository) CheckExistingHashes(hashes []string) ([]string, error) {
	if len(hashes) == 0 {
		return []string{}, nil
	}

	// Create placeholders for IN clause
	placeholders := make([]string, len(hashes))
	args := make([]interface{}, len(hashes))
	for i, hash := range hashes {
		placeholders[i] = "?"
		args[i] = hash
	}

	query := fmt.Sprintf("SELECT tip_hash FROM hrm_trans_import_presensi WHERE tip_hash IN (%s)", strings.Join(placeholders, ","))

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.Printf("checking existing hashes error: %v\n", err)
		return nil, err
	}

	var existingHashes []string
	for _, result := range results {
		if hash, ok := result["tip_hash"].(string); ok {
			existingHashes = append(existingHashes, hash)
		}
	}

	return existingHashes, nil
}

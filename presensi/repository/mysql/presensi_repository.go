package mysql

import (
	"database/sql"
	"encoding/json"
	"log"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLPresensiRepository struct {
	mysql.Repository
}

// NewMySQLPresensiRepository func
func NewMySQLPresensiRepository(conn *sql.DB) domain.PresensiRepository {
	return &mySQLPresensiRepository{mysql.Repository{Conn: conn}}
}

func (m *mySQLPresensiRepository) Fetch() ([]domain.Presensi, error) {
	results, err := m.QueryArrayOld("select * from hrm_trans_import_presensi")
	if err != nil {
		log.Printf("getting schedule error: %v\n", err)
		return nil, err
	}

	log.Printf("total data: %d", len(results))

	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.Printf("marshalling error: %v\n", err)
	}

	var result []domain.Presensi
	err = json.Unmarshal(resultJSON, &result)
	if err != nil {
		log.Printf("unmarshalling error: %v\n", err)
	}
	return result, nil
}

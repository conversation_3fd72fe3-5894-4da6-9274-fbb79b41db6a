package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLPresensiRepository struct {
	mysql.Repository
}

// NewMySQLPresensiRepository func
func NewMySQLPresensiRepository(conn *sql.DB) domain.PresensiRepository {
	return &mySQLPresensiRepository{mysql.Repository{Conn: conn}}
}

func (m *mySQLPresensiRepository) Fetch() ([]domain.Presensi, error) {
	results, err := m.QueryArrayOld("select * from hrm_trans_import_presensi")
	if err != nil {
		log.Printf("getting schedule error: %v\n", err)
		return nil, err
	}

	log.Printf("total data: %d", len(results))

	resultJSON, err := json.<PERSON>(results)
	if err != nil {
		log.Printf("marshalling error: %v\n", err)
	}

	var result []domain.Presensi
	err = json.Unmarshal(resultJSON, &result)
	if err != nil {
		log.Printf("unmarshalling error: %v\n", err)
	}
	return result, nil
}

func (m *mySQLPresensiRepository) ValidateEmployeeNIKs(niks []string) ([]domain.EmployeeNIK, error) {
	if len(niks) == 0 {
		return []domain.EmployeeNIK{}, nil
	}

	// Create placeholders for IN clause
	placeholders := make([]string, len(niks))
	args := make([]any, len(niks))
	for i, nik := range niks {
		placeholders[i] = "?"
		args[i] = nik
	}

	query := fmt.Sprintf("SELECT employee_fkid, nik FROM hrm_employee WHERE nik IN (%s)", strings.Join(placeholders, ","))

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.Printf("validating employee NIKs error: %v\n", err)
		return nil, err
	}

	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.Printf("marshalling employee NIKs error: %v\n", err)
		return nil, err
	}

	var employeeNIKs []domain.EmployeeNIK
	err = json.Unmarshal(resultJSON, &employeeNIKs)
	if err != nil {
		log.Printf("unmarshalling employee NIKs error: %v\n", err)
		return nil, err
	}

	return employeeNIKs, nil
}

func (m *mySQLPresensiRepository) CheckExistingHashes(hashes []string) ([]string, error) {
	if len(hashes) == 0 {
		return []string{}, nil
	}

	// Create placeholders for IN clause
	placeholders := make([]string, len(hashes))
	args := make([]interface{}, len(hashes))
	for i, hash := range hashes {
		placeholders[i] = "?"
		args[i] = hash
	}

	query := fmt.Sprintf("SELECT tip_hash FROM hrm_trans_import_presensi WHERE tip_hash IN (%s)", strings.Join(placeholders, ","))

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.Printf("checking existing hashes error: %v\n", err)
		return nil, err
	}

	var existingHashes []string
	for _, result := range results {
		if hash, ok := result["tip_hash"].(string); ok {
			existingHashes = append(existingHashes, hash)
		}
	}

	return existingHashes, nil
}

func (m *mySQLPresensiRepository) InsertPresensiData(data []domain.PresensiTransformed, overwrite bool) error {
	m.WithTransaction(func(tx mysql.Transaction) error {
		// tx.Delete()
		// tx.BulkInsert()
		return nil
	})

	if len(data) == 0 {
		return nil
	}

	// Begin transaction
	tx, err := m.Conn.Begin()
	if err != nil {
		log.Printf("error starting transaction: %v\n", err)
		return fmt.Errorf("failed to start transaction: %v", err)
	}

	// Ensure transaction is rolled back if not committed
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			log.Printf("transaction rolled back due to panic: %v\n", r)
		}
	}()

	// If overwrite is true, delete existing records first
	if overwrite {
		var hashes []string
		for _, record := range data {
			hashes = append(hashes, record.TipHash)
		}

		if len(hashes) > 0 {
			// Delete existing records with same hashes
			placeholders := make([]string, len(hashes))
			args := make([]interface{}, len(hashes))
			for i, hash := range hashes {
				placeholders[i] = "?"
				args[i] = hash
			}

			deleteQuery := fmt.Sprintf("DELETE FROM hrm_trans_import_presensi WHERE tip_hash IN (%s)", strings.Join(placeholders, ","))
			deleteResult, err := tx.Exec(deleteQuery, args...)
			if err != nil {
				tx.Rollback()
				log.Printf("error deleting existing records: %v\n", err)
				return fmt.Errorf("failed to delete existing records: %v", err)
			}

			deletedRows, err := deleteResult.RowsAffected()
			if err != nil {
				log.Printf("error getting deleted rows count: %v\n", err)
			} else {
				log.Printf("deleted %d existing records for overwrite", deletedRows)
			}
		}
	}

	// Prepare bulk insert query
	query := `INSERT INTO hrm_trans_import_presensi
		(tip_outlet_id, tip_nik, tip_nama_karyawan, tip_tanggal, tip_jam, tip_kode, tip_hash, employee_id)
		VALUES `

	var values []string
	var args []interface{}

	for _, record := range data {
		values = append(values, "(?, ?, ?, ?, ?, ?, ?, ?)")
		args = append(args,
			record.TipOutletID,
			record.TipNik,
			record.TipNamaKaryawan,
			record.TipTanggal,
			record.TipJam,
			record.TipKode,
			record.TipHash,
			record.EmployeeID,
		)
	}

	query += strings.Join(values, ", ")

	// Execute the bulk insert within transaction
	result, err := tx.Exec(query, args...)
	if err != nil {
		tx.Rollback()
		log.Printf("error inserting presensi data: %v\n", err)
		return fmt.Errorf("failed to insert presensi data: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Printf("error getting rows affected: %v\n", err)
	} else {
		log.Printf("successfully inserted %d presensi records", rowsAffected)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		log.Printf("error committing transaction: %v\n", err)
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	log.Printf("transaction committed successfully")
	return nil
}

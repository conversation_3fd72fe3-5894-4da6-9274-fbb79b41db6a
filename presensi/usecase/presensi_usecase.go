package usecase

import (
	"fmt"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
)

type presensiUseCase struct {
	presensiRepository domain.PresensiRepository
}

// NewPresensiUseCase funcs
func NewPresensiUseCase(s domain.PresensiRepository) domain.PresensiUseCase {
	return &presensiUseCase{presensiRepository: s}
}

func (s *presensiUseCase) Fetch() ([]domain.Presensi, error) {
	allPresensi, err := s.presensiRepository.Fetch()
	if err != nil {
		return nil, err
	}

	return allPresensi, nil
}

func (s *presensiUseCase) ImportPresensi(outletID int, overwrite bool, filePath string) error {
	// Parse Excel file to extract presensi data
	presensiData, err := s.parsePresensiExcelFile(filePath)
	if err != nil {
		return fmt.Errorf("error parsing Excel file: %v", err)
	}

	// Process the imported presensi data
	err = s.processPresensiImport(outletID, overwrite, presensiData)
	if err != nil {
		return fmt.Errorf("error processing presensi import: %v", err)
	}

	log.Info("Successfully imported %d presensi records for outlet %d", len(presensiData), outletID)
	return nil
}

// parsePresensiExcelFile parses the Excel file and returns a slice of PresensiImport
func (s *presensiUseCase) parsePresensiExcelFile(filePath string) ([]domain.PresensiImport, error) {
	// Open Excel file
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer f.Close()

	// Get the first sheet
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, fmt.Errorf("no sheets found in Excel file")
	}

	// Get all rows
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("failed to get rows: %v", err)
	}

	// Check if there are enough rows (at least header + 1 data row)
	if len(rows) < 2 {
		return nil, fmt.Errorf("excel file must contain at least a header row and one data row")
	}

	// Define expected headers
	expectedHeaders := []string{"NIK", "NAMA KARYAWAN", "TANGGAL", "JAM", "KETERANGAN"}

	// Find header row and create column mapping
	var headerRow []string
	var columnMap map[string]int
	var headerRowIndex int = -1

	// Look for header row (can be at any position)
	for i, row := range rows {
		if len(row) < len(expectedHeaders) {
			continue
		}

		// Check if this row contains all expected headers
		isHeaderRow := true
		tempColumnMap := make(map[string]int)

		for j, cell := range row {
			cellUpper := strings.ToUpper(strings.TrimSpace(cell))
			found := false
			for _, expectedHeader := range expectedHeaders {
				if cellUpper == strings.ToUpper(expectedHeader) {
					tempColumnMap[strings.ToUpper(expectedHeader)] = j
					found = true
					break
				}
			}
			// If we found all expected headers in this row, it's our header row
			if j < len(expectedHeaders) && !found {
				isHeaderRow = false
				break
			}
		}

		// Check if all expected headers are found
		if isHeaderRow && len(tempColumnMap) == len(expectedHeaders) {
			headerRow = row
			columnMap = tempColumnMap
			headerRowIndex = i
			break
		}
	}

	// Validate that header row was found
	if headerRow == nil || headerRowIndex == -1 {
		return nil, fmt.Errorf("excel file must contain header row with columns: %s", strings.Join(expectedHeaders, ", "))
	}

	log.Info("Found header row at index %d with columns: %v", headerRowIndex, columnMap)

	// Process data rows (skip header row and any rows before it)
	var presensiData []domain.PresensiImport
	for i, row := range rows {
		// Skip rows before and including header row
		if i <= headerRowIndex {
			continue
		}

		// Skip empty rows
		if len(row) < len(expectedHeaders) || strings.TrimSpace(row[0]) == "" {
			continue
		}

		// Create presensi import data using column mapping
		presensi := domain.PresensiImport{
			NIK:          strings.TrimSpace(row[columnMap["NIK"]]),
			NamaKaryawan: strings.TrimSpace(row[columnMap["NAMA KARYAWAN"]]),
			Tanggal:      strings.TrimSpace(row[columnMap["TANGGAL"]]),
			Jam:          strings.TrimSpace(row[columnMap["JAM"]]),
			Keterangan:   strings.TrimSpace(row[columnMap["KETERANGAN"]]),
		}

		// Basic validation for required fields
		if presensi.NIK == "" {
			return nil, fmt.Errorf("row %d: NIK cannot be empty", i+1)
		}
		if presensi.NamaKaryawan == "" {
			return nil, fmt.Errorf("row %d: NAMA KARYAWAN cannot be empty", i+1)
		}
		if presensi.Tanggal == "" {
			return nil, fmt.Errorf("row %d: TANGGAL cannot be empty", i+1)
		}
		if presensi.Jam == "" {
			return nil, fmt.Errorf("row %d: JAM cannot be empty", i+1)
		}

		presensiData = append(presensiData, presensi)
	}

	if len(presensiData) == 0 {
		return nil, fmt.Errorf("no valid presensi data found in Excel file")
	}

	log.Info("Successfully parsed %d presensi records from Excel file", len(presensiData))
	return presensiData, nil
}

// processPresensiImport processes the parsed presensi data
func (s *presensiUseCase) processPresensiImport(outletID int, overwrite bool, presensiData []domain.PresensiImport) error {
	log.Info("Processing %d presensi records for outlet %d (overwrite: %v)", len(presensiData), outletID, overwrite)

	// 1. Validate employee NIKs
	err := s.validateEmployeeNIKs(presensiData)
	if err != nil {
		return fmt.Errorf("NIK validation failed: %v", err)
	}

	// 2. Validate and format dates and times
	err = s.validateAndFormatDatesAndTimes(presensiData)
	if err != nil {
		return fmt.Errorf("date/time validation failed: %v", err)
	}

	// TODO: Continue with the rest of the implementation
	// 3. Check for duplicates if overwrite is false
	// 4. Transform data to match database schema
	// 5. Insert/update records in database

	log.Info("Successfully validated %d presensi records", len(presensiData))
	return nil
}

// validateEmployeeNIKs validates that all NIKs exist in the database
func (s *presensiUseCase) validateEmployeeNIKs(presensiData []domain.PresensiImport) error {
	// Extract unique NIKs from presensi data
	nikSet := make(map[string]bool)
	var niks []string

	for _, presensi := range presensiData {
		if !nikSet[presensi.NIK] {
			nikSet[presensi.NIK] = true
			niks = append(niks, presensi.NIK)
		}
	}

	// Query database for existing NIKs
	existingNIKs, err := s.presensiRepository.ValidateEmployeeNIKs(niks)
	if err != nil {
		return fmt.Errorf("error querying employee NIKs: %v", err)
	}

	// Create a map of existing NIKs for quick lookup
	existingNIKMap := make(map[string]domain.EmployeeNIK)
	for _, empNIK := range existingNIKs {
		existingNIKMap[empNIK.NIK] = empNIK
	}

	// Check for missing NIKs
	var missingNIKs []string
	for _, nik := range niks {
		if _, exists := existingNIKMap[nik]; !exists {
			missingNIKs = append(missingNIKs, nik)
		}
	}

	if len(missingNIKs) > 0 {
		return fmt.Errorf("the following NIKs do not exist in the database: %s", strings.Join(missingNIKs, ", "))
	}

	log.Info("All %d NIKs validated successfully", len(niks))
	return nil
}

// validateAndFormatDatesAndTimes validates and formats dates and times in presensi data
func (s *presensiUseCase) validateAndFormatDatesAndTimes(presensiData []domain.PresensiImport) error {
	for i := range presensiData {
		// Format date
		formattedDate, err := formatDate(presensiData[i].Tanggal)
		if err != nil {
			return fmt.Errorf("error formatting date for record %d (NIK: %s): %v", i+1, presensiData[i].NIK, err)
		}

		// Validate the formatted date can be parsed
		_, err = time.Parse("2006-01-02", formattedDate)
		if err != nil {
			return fmt.Errorf("invalid date format for record %d (NIK: %s, Date: %s): %v", i+1, presensiData[i].NIK, presensiData[i].Tanggal, err)
		}

		// Update the date in the struct
		presensiData[i].Tanggal = formattedDate

		// Validate time format (basic validation - should be HH:MM or HH:MM:SS)
		timeStr := strings.TrimSpace(presensiData[i].Jam)
		if timeStr == "" {
			return fmt.Errorf("time cannot be empty for record %d (NIK: %s)", i+1, presensiData[i].NIK)
		}

		// Try to parse time in different formats
		var timeValid bool
		timeFormats := []string{"15:04", "15:04:05", "3:04 PM", "3:04:05 PM"}
		for _, format := range timeFormats {
			if _, err := time.Parse(format, timeStr); err == nil {
				timeValid = true
				break
			}
		}

		if !timeValid {
			return fmt.Errorf("invalid time format for record %d (NIK: %s, Time: %s). Expected formats: HH:MM, HH:MM:SS, H:MM AM/PM", i+1, presensiData[i].NIK, timeStr)
		}

		// Update the time in the struct (keep original format for now)
		presensiData[i].Jam = timeStr
	}

	log.Info("All dates and times validated and formatted successfully")
	return nil
}

// formatDate handles different date formats and converts them to YYYY-MM-DD format
func formatDate(dateStr string) (string, error) {
	// Handle different date formats as in the original PHP code
	if len(dateStr) >= 5 && dateStr[4:5] == "/" {
		// Format: YYYY/MM/DD
		parts := strings.Split(dateStr, "/")
		return strings.Join(parts, "-"), nil
	} else if len(dateStr) >= 5 && dateStr[4:5] == "-" {
		// Format: YYYY-MM-DD
		return dateStr, nil
	} else if len(dateStr) >= 3 && dateStr[2:3] == "/" {
		// Format: DD/MM/YYYY
		parts := strings.Split(dateStr, "/")
		if len(parts) == 3 {
			return fmt.Sprintf("%s-%s-%s", parts[2], parts[1], parts[0]), nil
		}
	} else if len(dateStr) >= 3 && dateStr[2:3] == "-" {
		// Format: DD-MM-YYYY
		parts := strings.Split(dateStr, "-")
		if len(parts) == 3 {
			// Try to parse as DD-MM-YYYY first
			if _, err := time.Parse("02-01-2006", dateStr); err == nil {
				return fmt.Sprintf("%s-%s-%s", parts[2], parts[1], parts[0]), nil
			}
			// Try MM-DD-YYYY
			return fmt.Sprintf("%s-%s-%s", parts[2], parts[0], parts[1]), nil
		}
	}

	return dateStr, nil
}

package usecase

import (
	"gitlab.com/backend/api-hrm/domain"
)

type presensiUseCase struct {
	presensiRepository domain.PresensiRepository
}

// NewPresensiUseCase funcs
func NewPresensiUseCase(s domain.PresensiRepository) domain.PresensiUseCase {
	return &presensiUseCase{presensiRepository: s}
}

func (s *presensiUseCase) Fetch() ([]domain.Presensi, error) {
	allPresensi, err := s.presensiRepository.Fetch()
	if err != nil {
		return nil, err
	}

	return allPresensi, nil
}

package usecase

import (
	"fmt"
	"strings"

	"github.com/xuri/excelize/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
)

type presensiUseCase struct {
	presensiRepository domain.PresensiRepository
}

// NewPresensiUseCase funcs
func NewPresensiUseCase(s domain.PresensiRepository) domain.PresensiUseCase {
	return &presensiUseCase{presensiRepository: s}
}

func (s *presensiUseCase) Fetch() ([]domain.Presensi, error) {
	allPresensi, err := s.presensiRepository.Fetch()
	if err != nil {
		return nil, err
	}

	return allPresensi, nil
}

func (s *presensiUseCase) ImportPresensi(outletID int, overwrite bool, filePath string) error {
	// Parse Excel file to extract presensi data
	presensiData, err := s.parsePresensiExcelFile(filePath)
	if err != nil {
		return fmt.Errorf("error parsing Excel file: %v", err)
	}

	// Process the imported presensi data
	err = s.processPresensiImport(outletID, overwrite, presensiData)
	if err != nil {
		return fmt.Errorf("error processing presensi import: %v", err)
	}

	log.Info("Successfully imported %d presensi records for outlet %d", len(presensiData), outletID)
	return nil
}

// parsePresensiExcelFile parses the Excel file and returns a slice of PresensiImport
func (s *presensiUseCase) parsePresensiExcelFile(filePath string) ([]domain.PresensiImport, error) {
	// Open Excel file
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer f.Close()

	// Get the first sheet
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, fmt.Errorf("no sheets found in Excel file")
	}

	// Get all rows
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("failed to get rows: %v", err)
	}

	// Check if there are enough rows (at least header + 1 data row)
	if len(rows) < 2 {
		return nil, fmt.Errorf("excel file must contain at least a header row and one data row")
	}

	// Define expected headers
	expectedHeaders := []string{"NIK", "NAMA KARYAWAN", "TANGGAL", "JAM", "KETERANGAN"}

	// Find header row and create column mapping
	var headerRow []string
	var columnMap map[string]int
	var headerRowIndex int = -1

	// Look for header row (can be at any position)
	for i, row := range rows {
		if len(row) < len(expectedHeaders) {
			continue
		}

		// Check if this row contains all expected headers
		isHeaderRow := true
		tempColumnMap := make(map[string]int)

		for j, cell := range row {
			cellUpper := strings.ToUpper(strings.TrimSpace(cell))
			found := false
			for _, expectedHeader := range expectedHeaders {
				if cellUpper == strings.ToUpper(expectedHeader) {
					tempColumnMap[strings.ToUpper(expectedHeader)] = j
					found = true
					break
				}
			}
			// If we found all expected headers in this row, it's our header row
			if j < len(expectedHeaders) && !found {
				isHeaderRow = false
				break
			}
		}

		// Check if all expected headers are found
		if isHeaderRow && len(tempColumnMap) == len(expectedHeaders) {
			headerRow = row
			columnMap = tempColumnMap
			headerRowIndex = i
			break
		}
	}

	// Validate that header row was found
	if headerRow == nil || headerRowIndex == -1 {
		return nil, fmt.Errorf("excel file must contain header row with columns: %s", strings.Join(expectedHeaders, ", "))
	}

	log.Info("Found header row at index %d with columns: %v", headerRowIndex, columnMap)

	// Process data rows (skip header row and any rows before it)
	var presensiData []domain.PresensiImport
	for i, row := range rows {
		// Skip rows before and including header row
		if i <= headerRowIndex {
			continue
		}

		// Skip empty rows
		if len(row) < len(expectedHeaders) || strings.TrimSpace(row[0]) == "" {
			continue
		}

		// Create presensi import data using column mapping
		presensi := domain.PresensiImport{
			NIK:          strings.TrimSpace(row[columnMap["NIK"]]),
			NamaKaryawan: strings.TrimSpace(row[columnMap["NAMA KARYAWAN"]]),
			Tanggal:      strings.TrimSpace(row[columnMap["TANGGAL"]]),
			Jam:          strings.TrimSpace(row[columnMap["JAM"]]),
			Keterangan:   strings.TrimSpace(row[columnMap["KETERANGAN"]]),
		}

		// Basic validation for required fields
		if presensi.NIK == "" {
			return nil, fmt.Errorf("row %d: NIK cannot be empty", i+1)
		}
		if presensi.NamaKaryawan == "" {
			return nil, fmt.Errorf("row %d: NAMA KARYAWAN cannot be empty", i+1)
		}
		if presensi.Tanggal == "" {
			return nil, fmt.Errorf("row %d: TANGGAL cannot be empty", i+1)
		}
		if presensi.Jam == "" {
			return nil, fmt.Errorf("row %d: JAM cannot be empty", i+1)
		}

		presensiData = append(presensiData, presensi)
	}

	if len(presensiData) == 0 {
		return nil, fmt.Errorf("no valid presensi data found in Excel file")
	}

	log.Info("Successfully parsed %d presensi records from Excel file", len(presensiData))
	return presensiData, nil
}

// processPresensiImport processes the parsed presensi data
func (s *presensiUseCase) processPresensiImport(outletID int, overwrite bool, presensiData []domain.PresensiImport) error {
	// TODO: Implement the actual processing logic
	// This could include:
	// 1. Validate employee NIK exists
	// 2. Validate date and time formats
	// 3. Check for duplicates if overwrite is false
	// 4. Transform data to match database schema
	// 5. Insert/update records in database

	log.Info("Processing %d presensi records for outlet %d (overwrite: %v)", len(presensiData), outletID, overwrite)

	// For now, just log the data that would be processed
	for i, presensi := range presensiData {
		log.Info("Record %d: NIK=%s, Name=%s, Date=%s, Time=%s, Keterangan=%s",
			i+1, presensi.NIK, presensi.NamaKaryawan, presensi.Tanggal, presensi.Jam, presensi.Keterangan)
	}

	return nil
}

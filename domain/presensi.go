package domain

// Presensi struct
type Presensi struct {
	TipID               string `json:"tip_id"`
	TipOutletID         string `json:"tip_outlet_id"`
	TipNik              string `json:"tip_nik"`
	TipNamaKaryawan     string `json:"tip_name_ka<PERSON>wan"`
	TipTanggal          string `json:"tip_tanggal"`
	TipJam              string `json:"tip_jam"`
	TipKode             string `json:"tip_kode"`
	TipHash             string `json:"tip_hash"`
	EmployeeID          string `json:"employee_id"`
	TipIsTerlambat      string `json:"tip_is_terlambat"`
	TipConfirmTerlambat string `json:"tip_confirm_terlambat"`
	TipeTerlambat       string `json:"tipe_terlambat"`
	LateBreakIn         string `json:"late_break_in"`
	ConfLateBreak       string `json:"conf_late_break"`
	Overtime            string `json:"overtime"`
	Promise             string `json:"promise"`
}

// PresensiContract interface
type PresensiContract interface {
	Fetch() ([]Presensi, error)
}

// PresensiUseCase struct
type PresensiUseCase interface {
	PresensiContract
}

// PresensiRepository struct
type PresensiRepository interface {
	PresensiContract
}

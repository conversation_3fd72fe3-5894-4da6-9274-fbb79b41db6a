variables:
  DOCKER_DRIVER: "overlay2"
  IMAGE_TAG: ${CI_REGISTRY}/${CI_PROJECT_PATH}:${CI_COMMIT_REF_NAME}-latest

stages:
  - build
  - deploy

.docker_base:
  image: docker/compose:alpine-1.29.2
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  after_script:
    - docker logout ${CI_REGISTRY}

build_image:
  stage: build
  image: docker:26.1.1-alpine3.19
  extends: .docker_base
  # only:
  #   - dev
  #   - master
  #   - staging
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "staging"'
  services:
    - docker:26.1.1-dind-alpine3.19
  script:
    - docker build -t ${IMAGE_TAG} --pull .
    - docker push ${IMAGE_TAG}

deploy_dev:
  stage: deploy
  extends: .docker_base
  environment:
    name: development
  script:
    - mkdir -p /data/hrm
    - cat ${RSA_PUB_KEY} > /data/hrm/pub.key
    - cat ${RSA_PRIV_KEY} >  /data/hrm/priv.key
    - cat ${FIREBASE_CRED_FILE} > /data/hrm/firebase.json
    - cat ${ENV} > .env
    - echo ${G_GOOGLE_STORAGE_KEY} | base64 -d > /data/hrm/uniq-187911-4d685111920f.json
    - docker pull ${IMAGE_TAG}
    - docker container rm -f $CI_PROJECT_NAME || true
    - docker run --name ${CI_PROJECT_NAME} --network uniq-network --restart unless-stopped -d --env-file=.env -v /docker/runner/data/hrm:/assets/rsa --log-driver=gcplogs --log-opt gcp-project=uniq-187911  ${IMAGE_TAG}
  only:
    - dev
  tags:
    - testing-docker


deploy_staging:
  stage: deploy
  image: docker/compose:1.25.5
  # when: manual
  environment:
    name: staging
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - mkdir -p /data/hrm
    - cat ${RSA_PUB_KEY} > /data/hrm/pub.key
    - cat ${RSA_PRIV_KEY} >  /data/hrm/priv.key
    - cat ${FIREBASE_CRED_FILE} > /data/hrm/firebase.json
    - cat ${ENV} > .env
    - echo ${G_GOOGLE_STORAGE_KEY} | base64 -d > /data/hrm/uniq-187911-4d685111920f.json
    - docker pull ${IMAGE_TAG}
    - docker container rm -f $CI_PROJECT_NAME || true
    - docker run --restart unless-stopped --name $CI_PROJECT_NAME --network uniq-network -d -p 7250:80 --env-file=.env -v /docker/runner/data/hrm:/assets/rsa --log-driver=gcplogs --log-opt gcp-project=uniq-187911  ${IMAGE_TAG}
  only:
    - staging
  tags:
    - staging


deploy_production:
  stage: deploy
  image: docker/compose:1.25.5
  # when: manual
  environment:
    name: production
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - mkdir -p /data/hrm
    - cat ${RSA_PUB_KEY} > /data/hrm/pub.key
    - cat ${RSA_PRIV_KEY} >  /data/hrm/priv.key
    - cat ${FIREBASE_CRED_FILE} > /data/hrm/firebase.json
    - cat $ENV > .env
    - echo ${G_GOOGLE_STORAGE_KEY} | base64 -d > /data/hrm/uniq-187911-4d685111920f.json
    - docker pull ${IMAGE_TAG}
    - docker container rm -f $CI_PROJECT_NAME || true
    - docker run --restart unless-stopped --name $CI_PROJECT_NAME --network uniq-network -d -p 7250:80 --env-file=.env -v /docker/runner/data/hrm:/assets/rsa --log-driver=gcplogs --log-opt gcp-project=uniq-187911  ${IMAGE_TAG}
  only:
    - master
  tags:
    - production
